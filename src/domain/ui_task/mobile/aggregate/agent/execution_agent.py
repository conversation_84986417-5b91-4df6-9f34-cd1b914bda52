#!/usr/bin/env python3
"""
执行Agent

负责为需要坐标的动作补充坐标并执行具体操作
"""

import re
import time
from typing import Dict, Any, Tuple, List

from loguru import logger

from src.domain.ui_task.mobile.android.action_tool import (
    execute_simple_action,
    ScreenUtils,
    parse_action_with_coordinates,
    validate_action
)
from src.domain.ui_task.mobile.android.image_processor import image_annotator
from src.domain.ui_task.mobile.android.screenshot_manager import convert_screenshot_to_base64, take_screenshot
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.infra.model import get_chat_model, get_coordinate_model, \
    get_coordinate_model_name, get_vision_model, get_vision_model_name


class ExecutionAgent:
    """执行Agent - 负责坐标补充和动作执行"""

    def __init__(self):
        # 使用配置化的坐标模型
        self.coordinate_client = get_coordinate_model()
        self.coordinate_model_name = get_coordinate_model_name()
        # 使用配置化的视觉模型（用于分步执行）
        self.vision_client = get_vision_model()
        self.vision_model_name = get_vision_model_name()
        self.model = get_chat_model()
        # 延迟导入避免循环依赖
        self._verification_agent = None

    @staticmethod
    def requires_coordinates(action_str: str) -> bool:
        """
        判断动作是否需要坐标

        Args:
            action_str: 动作字符串

        Returns:
            是否需要坐标
        """
        action_str_lower = action_str.lower()

        # 需要坐标的动作类型
        coordinate_actions = [
            'click', 'long_press', 'drag', 'scroll'
        ]

        # 不需要坐标的动作类型
        non_coordinate_actions = [
            'wait', 'back', 'type', 'delete', 'enter', 'finished'
        ]

        # 检查是否包含需要坐标的动作
        for action in coordinate_actions:
            if action in action_str_lower:
                return True

        # 检查是否包含不需要坐标的动作
        for action in non_coordinate_actions:
            if action in action_str_lower:
                return False

        # 默认情况下，如果无法确定，返回False（不需要坐标）
        return False

    def get_coordinates_from_model(self, decision_fields: Dict[str, Any], action_command: str, image_data_base64: str,
                                   task_id: str, state: DeploymentState, max_retries: int = 3):
        """
        使用坐标模型根据动作决策和命令获取坐标

        Args:
            decision_fields: 决策agent的结构化字段
            action_command: 动作命令
            image_data_base64: 当前截图的base64数据
            task_id: 任务ID
            state: DeploymentState
            max_retries: 最大重试次数

        Returns:
            带坐标的动作字符串
        """
        # 获取设备ID，用于屏幕尺寸检测
        device = state.get("device")

        for attempt in range(max_retries + 1):
            try:
                logger.info(f"[task_id: {task_id}] 🎯 坐标获取尝试 {attempt + 1}/{max_retries + 1}")

                coordinate_messages = ExecutionAgent._build_coordinate_messages(
                    decision_fields, action_command, image_data_base64
                )

                # 调用坐标模型
                chat_completion = self.coordinate_client.chat.completions.create(
                    model=self.coordinate_model_name,
                    messages=coordinate_messages,
                    temperature=0,
                    stream=True,
                    max_tokens=4096
                )
                coordinate_response = ""
                for message in chat_completion:
                    if message.choices[0].delta.content:
                        coordinate_response += message.choices[0].delta.content

                logger.info(
                    f"[task_id: {task_id}] Coordinate model response (attempt {attempt + 1}): \n {coordinate_response}")

                # 解析坐标模型的思考过程和动作
                coordinate_thought, coordinate_action, wrong_reason = ExecutionAgent._parse_coordinate_response(
                    coordinate_response)

                # 如果存在WrongReason且不为空或"None"，跳过动作校验
                if wrong_reason and wrong_reason.strip() and wrong_reason.strip().lower() != "none":
                    logger.info(f"[task_id: {task_id}] ⚠️ 检测到WrongReason，跳过动作校验: {wrong_reason}")
                    # 创建一个表示跳过验证的validation_result
                    validation_result = {
                        "is_valid": True,
                        "errors": [],
                        "action_type_match": True,
                        "coordinates_valid": True,
                        "coordinates_in_bounds": True,
                        "skipped_due_to_wrong_reason": True
                    }
                    result = {
                        "action": coordinate_action,
                        "full_response": coordinate_response,
                        "thought": coordinate_thought,
                        "wrong_reason": wrong_reason,
                        "validation_result": validation_result,
                        "attempt": attempt + 1
                    }
                    logger.info(f"[task_id: {task_id}] ✅ 由于WrongReason跳过验证 (尝试 {attempt + 1}): {wrong_reason}")
                    return result

                # 验证结果
                validation_result = ExecutionAgent._validate_coordinate_result(
                    action_command, coordinate_action, task_id, device
                )

                if validation_result["is_valid"]:
                    # 验证通过，返回结果
                    result = {
                        "action": coordinate_action,
                        "full_response": coordinate_response,
                        "thought": coordinate_thought,
                        "wrong_reason": wrong_reason if wrong_reason and wrong_reason != "None" else "",
                        "validation_result": validation_result,
                        "attempt": attempt + 1
                    }
                    logger.info(f"[task_id: {task_id}] ✅ 坐标获取成功 (尝试 {attempt + 1}): {coordinate_action}")
                    return result
                else:
                    # 验证失败
                    if attempt < max_retries:
                        logger.warning(
                            f"[task_id: {task_id}] ⚠️ 坐标验证失败，准备重试 (尝试 {attempt + 1}): {', '.join(validation_result['errors'])}")
                        # 可以在这里添加延迟
                        continue
                    else:
                        # 达到最大重试次数
                        logger.error(
                            f"[task_id: {task_id}] ❌ 坐标获取失败，已达到最大重试次数: {', '.join(validation_result['errors'])}")
                        return {
                            "action": coordinate_action if coordinate_action else "",
                            "full_response": coordinate_response,
                            "thought": coordinate_thought,
                            "wrong_reason": wrong_reason,
                            "validation_result": validation_result,
                            "attempt": attempt + 1,
                            "error": "验证失败，已达到最大重试次数"
                        }

            except Exception as e:
                logger.error(f"[task_id: {task_id}] ❌ Error getting coordinates (attempt {attempt + 1}): {str(e)}")
                if attempt < max_retries:
                    continue
                else:
                    from src.domain.ui_task.mobile.utils.exception_handler import AgentExceptionHandler
                    return AgentExceptionHandler.handle_execution_agent_exception(
                        task_id, e, max_retries, attempt
                    )

    @staticmethod
    def _build_coordinate_messages(decision_fields: Dict[str, Any], action_command: str,
                                   image_data_base64: str) -> list:
        """
        构建坐标模型的消息列表，包含历史记录和前后图片对比

        Args:
            decision_fields: 决策agent的结构化字段
            action_command: 动作命令
            image_data_base64: 当前截图base64

        Returns:
            消息列表
        """
        messages = []

        # 从结构化字段中提取相关信息
        agent_instruction = decision_fields.get("instruction", "")

        user_instruction = decision_fields.get("current_step_name", "")

        # 根据action_command动态生成动作说明和参数要求
        action_instruction, parameter_instruction = ExecutionAgent._generate_dynamic_action_instruction(action_command)

        # 构建系统指令
        system_instruction = f"""
You are a GUI agent. You are given a task and your action history, with screenshots. You need to perform the next action to complete the task. 

## Action Space
{action_instruction} # {parameter_instruction}


## User Instruction
{agent_instruction}


## Note
- Use Chinese in `Thought` part.
- Write a small plan and finally summarize your next action (with its target element) in one sentence in `Thought` part.
- Perform operations based on the element corresponding to the approximate (non-precise) description in the user's instructions. If the element is not found, please describe the element you see at that position in one sentence as the reason for the error; output 'None' when the element is found.

## Output Format
```
Thought: ...
Action: ...
WrongReason: ...
```
"""
        # - Find page elements according to user instructions and perform operations on the elements. Summarize your next action in one sentence and output it to 'Thought'
        # - Write a small plan and finally summarize your next action (with its target element) in one sentence in `Thought` part.
        print(system_instruction)
        messages.append({
            "role": "user",
            "content": system_instruction
        })
        # 添加当前截图
        messages.append({
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_data_base64}",
                        "detail": "high"
                    },
                    "detail": "high",
                }
            ]
        })
        return messages

    @staticmethod
    def _get_history_parameters(state: DeploymentState) -> Tuple[List[Dict], bool]:
        """
        获取历史参数，仅获取当前步骤的执行历史

        Args:
            state: 当前状态

        Returns:
            Tuple[execution_records, has_execution_history]: 执行记录列表和是否有历史记录
        """
        history = state.get("history", [])
        current_step_index = state.get("current_step_index", 0)

        # 只获取当前步骤的执行记录
        execution_records = [r for r in history if
                             r.get("action") == "step_execution_with_ai" and
                             r.get("step_index") == current_step_index and
                             r.get("ai_response")]

        # 取最近2条记录（如果当前步骤有超过2条记录的话）
        recent_execution_records = execution_records
        has_execution_history = len(recent_execution_records) > 0
        return recent_execution_records, has_execution_history

    @staticmethod
    def _extract_thought_from_coordinate_response(coordinate_response: str) -> str:
        """
        从坐标响应中提取思考内容

        Args:
            coordinate_response: 坐标模型的完整响应

        Returns:
            思考内容
        """
        if not coordinate_response:
            return ""

        lines = coordinate_response.split('\n')
        thought_lines = []

        for i, line in enumerate(lines):
            if line.strip().startswith('Thought:') or line.strip().startswith('思考:'):
                # 提取思考内容（从当前行到Action行之前的所有内容）
                for j in range(i, len(lines)):
                    if lines[j].strip().startswith('Action:'):
                        break
                    thought_lines.append(lines[j])
                break

        thought_content = '\n'.join(thought_lines).strip()
        # 移除"Thought:"前缀
        if thought_content.startswith('Thought:'):
            thought_content = thought_content.replace('Thought:', '').strip()
        elif thought_content.startswith('思考:'):
            thought_content = thought_content.replace('思考:', '').strip()

        return thought_content

    @staticmethod
    def extract_thought_from_ai_response(ai_response: str) -> str:
        """
        从AI响应中提取思考内容，去掉Action等其他部分

        Args:
            ai_response: AI的完整响应内容

        Returns:
            只包含思考内容的字符串
        """
        if not ai_response:
            return ""

        lines = ai_response.split('\n')
        thought_lines = []
        in_thought_section = False

        for line in lines:
            stripped_line = line.strip()

            # 检查是否开始思考部分
            if stripped_line.startswith('Thought:') or stripped_line.startswith('思考:'):
                in_thought_section = True
                # 提取Thought:后面的内容
                if stripped_line.startswith('Thought:'):
                    thought_content = stripped_line.replace('Thought:', '').strip()
                else:
                    thought_content = stripped_line.replace('思考:', '').strip()

                if thought_content:  # 如果同一行有内容
                    thought_lines.append(thought_content)
                continue

            # 如果遇到Action行，停止收集
            if stripped_line.startswith('Action:') or stripped_line.startswith('动作:'):
                break

            # 如果在思考部分，收集内容
            if in_thought_section:
                thought_lines.append(line)

        return '\n'.join(thought_lines).strip()

    @staticmethod
    def _generate_dynamic_action_instruction(action_command: str) -> tuple[str, str]:
        """
        根据action_command动态生成动作说明和参数要求

        Args:
            action_command: 动作命令

        Returns:
            (动作说明, 参数补充要求)
        """
        action_command_lower = action_command.lower()

        # 点击动作
        if 'click' in action_command_lower:
            action_instruction = "click(point='<point>x1 y1</point>')"
            parameter_instruction = "为click动作补充点击位置的坐标"

        # 长按动作
        elif 'long_press' in action_command_lower:
            action_instruction = "long_press(point='<point>x1 y1</point>')"
            parameter_instruction = "为long_press动作补充长按位置的坐标"

        # 拖拽动作
        elif 'drag' in action_command_lower:
            action_instruction = "drag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>')"
            parameter_instruction = "为drag动作补充起始和结束位置的坐标"

        # 滑动动作
        elif 'scroll' in action_command_lower:
            # 从action_command_lower中提取方向参数
            direction_match = re.search(r"direction='([^']+)'", action_command_lower)
            if direction_match:
                direction = direction_match.group(1)
                action_instruction = f"scroll(point='<point>x1 y1</point>', direction='{direction}')"
            else:
                action_instruction = "scroll(point='<point>x1 y1</point>', direction='down or up or right or left')"
            parameter_instruction = "为scroll动作补充滑动起点坐标，不要改变direction参数"
        # 未知动作类型
        else:
            action_instruction = f"{action_command} - 当前动作"
            parameter_instruction = f"请根据{action_command}的类型补充相应参数，如果不需要坐标则直接输出原始命令"
        return action_instruction, parameter_instruction

    @staticmethod
    def _validate_coordinate_result(action_command: str, coordinate_action: str, task_id: str,
                                    device: str) -> Dict[str, Any]:
        """
        验证坐标模型的输出结果，使用现有的action_tool工具

        Args:
            action_command: 原始动作命令
            coordinate_action: 坐标模型输出的动作
            task_id: 任务ID
            device: 设备ID

        Returns:
            验证结果字典
        """
        validation_result = {
            "is_valid": True,
            "errors": [],
            "action_type_match": True,
            "coordinates_valid": True,
            "coordinates_in_bounds": True
        }

        if not coordinate_action or coordinate_action.strip() == "":
            validation_result["is_valid"] = False
            validation_result["errors"].append("坐标模型输出为空")
            return validation_result

        # 1. 检查动作类型是否一致
        original_action_type = action_command.lower().split('(')[0].strip()
        output_action_type = coordinate_action.lower().split('(')[0].strip()

        if original_action_type != output_action_type:
            validation_result["is_valid"] = False
            validation_result["action_type_match"] = False
            validation_result["errors"].append(
                f"动作类型不一致: 期望'{original_action_type}', 实际'{output_action_type}'")

        # 2. 使用现有的ActionParser解析动作，传递设备参数进行坐标归一化
        parsed_action = parse_action_with_coordinates(coordinate_action, device)

        if not parsed_action:
            validation_result["is_valid"] = False
            validation_result["coordinates_valid"] = False
            validation_result["errors"].append("无法解析坐标动作")
            return validation_result

        # 3. 使用现有的ActionValidator验证动作
        if not validate_action(parsed_action):
            validation_result["is_valid"] = False
            validation_result["coordinates_valid"] = False
            validation_result["errors"].append("动作验证失败，缺少必要参数")

        # 4. 检查坐标是否在屏幕范围内（使用现有的ScreenUtils）
        if ExecutionAgent.requires_coordinates(action_command):
            screen_width, screen_height = ScreenUtils.get_screen_size(device)

            # 检查各种坐标参数
            coord_params = ['x', 'y', 'start_x', 'start_y', 'end_x', 'end_y']
            for param in coord_params:
                if param in parsed_action:
                    coord_value = parsed_action[param]
                    if param in ['x', 'start_x', 'end_x']:  # x坐标
                        if coord_value < 0 or coord_value > screen_width:
                            validation_result["is_valid"] = False
                            validation_result["coordinates_in_bounds"] = False
                            validation_result["errors"].append(
                                f"X坐标超出屏幕范围: {param}={coord_value}, 屏幕宽度={screen_width}"
                            )
                    elif param in ['y', 'start_y', 'end_y']:  # y坐标
                        if coord_value < 0 or coord_value > screen_height:
                            validation_result["is_valid"] = False
                            validation_result["coordinates_in_bounds"] = False
                            validation_result["errors"].append(
                                f"Y坐标超出屏幕范围: {param}={coord_value}, 屏幕高度={screen_height}"
                            )

        if validation_result["is_valid"]:
            logger.info(f"[task_id: {task_id}] ✅ 坐标验证通过: {coordinate_action}")
        else:
            logger.warning(f"[task_id: {task_id}] ❌ 坐标验证失败: {', '.join(validation_result['errors'])}")

        return validation_result

    @staticmethod
    def _parse_coordinate_response(coordinate_response: str) -> Tuple[str, str, str]:
        thought_content = ""
        action_line = ""
        wrong_reason = ""

        lines = coordinate_response.split('\n')
        for i, line in enumerate(lines):
            if line.strip().startswith('Thought:') or line.strip().startswith('思考:'):
                # 提取思考内容（从当前行到Action行之前的所有内容）
                thought_lines = []
                for j in range(i, len(lines)):
                    if lines[j].strip().startswith('Action:'):
                        break
                    thought_lines.append(lines[j])
                thought_content = '\n'.join(thought_lines).strip()
            elif line.strip().startswith('Action:'):
                action_line = line.replace('Action:', '').strip()
            elif line.strip().startswith('WrongReason:'):
                wrong_reason = line.replace('WrongReason:', '').strip()

        return thought_content, action_line, wrong_reason

    def execute_action(self, action_line: str, decision_fields: Dict[str, Any], state: DeploymentState,
                       before_screenshot_path: str, current_action=None) -> \
            Dict[str, Any]:
        """
        执行动作，在动作执行前重新截图以确保图片是最新的

        Args:
            action_line: 动作命令
            decision_fields: 决策agent的结构化字段
            state: 当前状态
            before_screenshot_path: 当前截图的路径（保持兼容性，但会重新截图）
            current_action: 当前动作记录对象（用于更新数据库）

        Returns:
            执行结果
        """
        task_id = state["task_id"]

        # 在动作执行前重新截图，因为在模型思考过程中图片可能发生了变化
        logger.info(f"[task_id: {task_id}] 📸 Taking fresh screenshot before action execution...")
        fresh_screenshot_path = take_screenshot(
            device=state["device"],
            task_id=task_id,
            action_name="before_action_execution"
        )

        # 使用新截图的base64数据
        image_data_base64 = convert_screenshot_to_base64(fresh_screenshot_path, state["task_id"])

        # 开始时间
        start_time = time.time()
        needs_coordinates = ExecutionAgent.requires_coordinates(action_line)

        if needs_coordinates:
            logger.info(f"[task_id: {task_id}] 🎯 Action requires coordinates, using provided screenshot...")

            # 使用提供的截图数据获取坐标，包含验证和重试机制
            coordinate_result = self.get_coordinates_from_model(
                decision_fields, action_line, image_data_base64, task_id, state
            )

            # 处理坐标获取结果
            if isinstance(coordinate_result, dict):
                action_with_coordinates = coordinate_result.get("action", "")
                coordinate_response = coordinate_result.get("full_response", "")
                validation_result = coordinate_result.get("validation_result", {})
                attempt_count = coordinate_result.get("attempt", 1)
                coordinate_error = coordinate_result.get("error", "")
                wrong_reason = coordinate_result.get("wrong_reason", "")

                # 如果解析到了错误原因且不为空，设置到state中
                # 这样 _add_action_history_entry 方法会自动将其添加到执行历史中
                if wrong_reason and wrong_reason.strip():
                    state["verification_failure_reason"] = wrong_reason.strip()
                    logger.info(f"[task_id: {task_id}] 🔍 设置坐标模型错误原因到验证失败原因: {wrong_reason.strip()}")

                # 检查是否获取到有效的坐标
                if action_with_coordinates and validation_result.get("is_valid", False):
                    logger.info(
                        f"[task_id: {task_id}] ✅ 坐标获取成功 (尝试 {attempt_count}次): {action_with_coordinates}")
                    action_result = execute_simple_action(action_with_coordinates, state["device"])
                    action_result["coordinate_response"] = coordinate_response
                    action_result["coordinate_validation"] = validation_result
                    action_result["coordinate_attempts"] = attempt_count
                else:
                    # 坐标获取失败或验证失败
                    error_msg = coordinate_error or "坐标获取失败或验证失败"
                    validation_errors = validation_result.get("errors", [])
                    if validation_errors:
                        error_msg += f": {', '.join(validation_errors)}"

                    logger.exception(f"[task_id: {task_id}] ❌ {error_msg}")
                    action_result = {
                        "status": "error",
                        "message": error_msg,
                        "coordinate_response": coordinate_response,
                        "coordinate_validation": validation_result,
                        "coordinate_attempts": attempt_count,
                        "failed_action": action_with_coordinates
                    }
            else:
                # 兼容旧格式
                action_with_coordinates = coordinate_result if coordinate_result else ""
                if action_with_coordinates:
                    logger.info(f"[task_id: {task_id}] ✓ Got coordinates (legacy): {action_with_coordinates}")
                    action_result = execute_simple_action(action_with_coordinates, state["device"])
                else:
                    action_result = {
                        "status": "error",
                        "message": "Failed to get coordinates (legacy)",
                    }
            # 在模型思考后，动作执行完成后，再重新截一次图用于打标记和更新数据库
            logger.info(f"[task_id: {task_id}] 📸 Taking final screenshot after action execution for annotation and database update...")
            final_screenshot_path = take_screenshot(
                device=state["device"],
                task_id=task_id,
                action_name="after_action_execution"
            )

            # 执行截图标注和数据库更新，使用最终截图路径
            self._annotate_screenshot_after_execution(state, action_line, needs_coordinates,
                                                      coordinate_result if needs_coordinates else None,
                                                      final_screenshot_path, current_action)
        else:
            logger.info(f"[task_id: {task_id}] ⚡ Executing action without coordinates: {action_line}")
            action_result = execute_simple_action(action_line, state["device"])

            # 对于不需要坐标的动作，也在执行后重新截图并更新数据库
            logger.info(f"[task_id: {task_id}] 📸 Taking final screenshot after simple action execution...")
            final_screenshot_path = take_screenshot(
                device=state["device"],
                task_id=task_id,
                action_name="after_simple_action_execution"
            )

            # 更新数据库中的截图路径（不需要打标记）
            if current_action:
                self._update_action_screenshot(current_action, final_screenshot_path)
                logger.info(f"[{task_id}] 📝 Updated action screenshot path to: {final_screenshot_path}")

        # 结束时间
        end_time = time.time()
        logger.info(f"[task_id: {task_id}] 执行命令耗时: {end_time - start_time:.2f}s")

        # 立即记录执行日志
        self._log_execution_immediately(action_result, needs_coordinates, task_id)

        return action_result

    def _annotate_screenshot_after_execution(self, state: DeploymentState, action_line: str, needs_coordinates: bool,
                                             coordinate_result: Dict[str, Any] = None,
                                             current_screenshot: str = None, current_action=None) -> None:
        """
        在动作执行完成后对截图进行标注，并更新动作表中的截图路径

        Args:
            state: 当前状态
            action_line: 原始动作命令
            needs_coordinates: 是否需要坐标
            coordinate_result: 坐标获取结果（如果需要坐标）
            current_screenshot: 当前截图路径
            current_action: 当前动作记录对象
        """
        task_id = state["task_id"]

        try:
            # 无论是否需要标记，都先更新数据库中的截图路径，以便前端刷新展示
            action_record = current_action or getattr(state, 'current_action', None)
            if action_record and current_screenshot:
                self._update_action_screenshot(action_record, current_screenshot)
                logger.info(f"[{task_id}] 📝 Updated action screenshot path to: {current_screenshot}")

            # 如果需要坐标，则进行图片标注
            if needs_coordinates:
                # 确定要标注的动作命令
                final_action_command = action_line

                # 如果需要坐标且获取到了坐标，使用带坐标的动作命令
                if coordinate_result and isinstance(coordinate_result, dict):
                    action_with_coordinates = coordinate_result.get("action", "")
                    validation_result = coordinate_result.get("validation_result", {})

                    # 只有在坐标验证通过时才使用带坐标的动作
                    if action_with_coordinates and validation_result.get("is_valid", False):
                        final_action_command = action_with_coordinates

                # 执行标注（直接在原文件上标注，不返回路径）
                image_annotator.annotate_screenshot_from_action(
                    current_screenshot, final_action_command, device=state["device"]
                )
                logger.info(f"[{task_id}] 🎯 Screenshot annotated after execution: {final_action_command}")
            else:
                logger.info(f"[{task_id}] ℹ️ No annotation needed for action: {action_line}")

        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to process screenshot after execution: {str(e)}")

    def _log_execution_immediately(self, action_result: Dict[str, Any], needs_coordinates: bool, task_id: str):
        """立即记录执行日志"""
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService

            # 提取执行思考内容
            thought_content = "执行动作完成"
            if needs_coordinates and action_result:
                coordinate_response = action_result.get("coordinate_response", "")
                if coordinate_response:
                    extracted_thought = self._extract_thought_from_coordinate_response(coordinate_response)
                    if extracted_thought:
                        thought_content = extracted_thought

            execution_log = ExecutionLogService.create_execution_log(thought_content)
            task_persistence_service.append_execution_log_entries(task_id, [execution_log])
            logger.info(f"[{task_id}] 🔧 Execution logged immediately")

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to log execution: {str(e)}")

    def execute_step_with_ai_decision(self, state: DeploymentState, step_description: str,
                                      expected_result: Dict = None, max_step_attempts: int = 7) -> str:
        """
        使用AI决策执行单个步骤，让大模型自己发挥
        给大模型所有动作列表，让它自己判断直到finished或失败

        Args:
            state: 当前状态
            step_description: 步骤描述
            expected_result: 预期结果字典，包含text、image、wait_time
            max_step_attempts: 单个步骤最大尝试次数，默认10次

        Returns:
            "finished": 步骤完成
            "failed": 步骤失败
            "continue": 继续执行
        """
        task_id = state["task_id"]

        # 检查任务是否被停止
        if self._check_task_stopped(task_id):
            logger.info(f"[{task_id}] 🛑 Task stopped by user during step execution")
            return "failed"

        # 检查预期结果是否为空，如果为空则执行一次后直接结束
        is_empty_expected_result = self._is_empty_expected_result(expected_result)
        if is_empty_expected_result:
            logger.info(f"[{task_id}] 📝 Expected result is empty, will finish after one execution")

        # 检查当前步骤的执行次数
        current_step_index = state.get("current_step_index", 0)
        step_attempt_count = self._get_step_attempt_count(state, current_step_index)

        if step_attempt_count > max_step_attempts:
            error_msg = f"步骤执行次数超过最大限制({max_step_attempts}次)，主动退出"
            logger.error(f"[{task_id}] ❌ {error_msg}")

            # 更新任务状态为失败
            self._update_task_status_to_failed(task_id, error_msg)
            return "failed"

        logger.info(f"[{task_id}] 🔄 Step attempt {step_attempt_count + 1}/{max_step_attempts}")

        # 创建动作记录
        current_action = self._create_step_action_record(task_id, step_description)

        # 记录步骤开始日志
        self._log_step_start(task_id, step_description)

        try:
            screenshot_path = take_screenshot(
                device=state["device"],
                task_id=task_id,
                action_name="step_ai_decision"
            )

            # 更新动作记录的截图路径
            if current_action:
                self._update_action_screenshot(current_action, screenshot_path)
            image_data_base64 = convert_screenshot_to_base64(screenshot_path, task_id)

            # 构建给大模型的提示（不包含历史记录，历史记录通过messages数组传递）
            ai_prompt = self._build_step_execution_prompt(step_description, expected_result)

            # 构建包含历史对话的消息数组
            messages = self._build_step_execution_messages(ai_prompt, image_data_base64, state)

            # 调用视觉模型进行分步执行
            chat_completion = self.vision_client.chat.completions.create(
                model=self.vision_model_name,
                messages=messages,
                stream=True,
                temperature=0,
                max_tokens=4096
            )
            ai_response = ""
            for message in chat_completion:
                if message.choices[0].delta.content:
                    ai_response += message.choices[0].delta.content

            logger.info(f"[{task_id}] 🤖 AI response: {ai_response}")

            # 更新动作记录的决策内容
            if current_action:
                self._update_action_decision_content(current_action, ai_response)

            # 记录AI决策日志
            self._log_ai_decision(task_id, step_description, ai_response)

            # 解析AI响应，提取动作和状态
            parsed_result = self._parse_ai_response(ai_response)

            if not parsed_result:
                logger.warning(f"[{task_id}] ⚠️ Failed to parse AI response")
                # 记录失败的执行历史
                self._record_step_execution_history(state, step_description, ai_response, "", "failed")
                self._complete_step_action_record(current_action, False, "AI响应解析失败")
                return "continue"

            action_command = parsed_result.get("action")
            step_status = parsed_result.get("status", "continue")

            logger.info(f"[{task_id}] 🤖 AI Status: {step_status}")

            # 更新动作记录的动作命令
            if current_action and action_command:
                self._update_action_command(current_action, action_command)

            # 记录执行历史
            self._record_step_execution_history(state, step_description, ai_response, action_command, step_status)

            if step_status == "finished":
                logger.info(f"[{task_id}] ✅ AI determined step is finished")
                self._log_step_completion(task_id, step_description, True, "AI判断步骤完成")
                self._complete_step_action_record(current_action, True, "步骤完成")
                return "finished"
            elif step_status == "failed":
                logger.error(f"[{task_id}] ❌ AI determined step failed")
                self._log_step_completion(task_id, step_description, False, "AI判断步骤失败")
                self._complete_step_action_record(current_action, False, "AI判断步骤失败")
                return "failed"
            elif action_command:
                # 在执行动作前再次检查任务是否被停止
                if self._check_task_stopped(task_id):
                    logger.info(f"[{task_id}] 🛑 Task stopped by user before action execution")
                    self._complete_step_action_record(current_action, False, "用户停止任务")
                    return "failed"

                # 执行AI建议的动作
                execution_result = self._execute_parsed_action(action_command, state, task_id, current_action)

                if execution_result == "success":
                    logger.info(f"[{task_id}] ✅ Action executed successfully")
                    self._log_action_execution(task_id, action_command, True, "动作执行成功")
                    self._complete_step_action_record(current_action, True, "动作执行成功")

                    # 如果预期结果为空，执行一次后直接结束
                    if is_empty_expected_result:
                        logger.info(f"[{task_id}] ✅ Step finished due to empty expected result")
                        self._log_step_completion(task_id, step_description, True, "预期结果为空，执行一次后自动完成")
                        return "finished"

                    return "continue"
                elif execution_result == "failed":
                    logger.error(f"[{task_id}] ❌ Action execution failed")
                    self._log_action_execution(task_id, action_command, False, "动作执行失败")
                    self._complete_step_action_record(current_action, False, "动作执行失败")
                    return "failed"
                else:
                    self._log_action_execution(task_id, action_command, True, "动作执行继续")
                    self._complete_step_action_record(current_action, True, "动作执行继续")
                    return "continue"
            else:
                logger.warning(f"[{task_id}] ⚠️ No valid action from AI")
                self._complete_step_action_record(current_action, True, "无有效动作")
                return "continue"

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error in AI decision execution: {str(e)}")
            # 记录异常的执行历史
            self._record_step_execution_history(state, step_description, str(e), "", "error")
            # 记录异常日志
            self._log_execution_error(task_id, step_description, str(e))
            self._complete_step_action_record(current_action, False, f"执行异常: {str(e)}")
            return "failed"
        finally:
            wait_time = 2.5
            if expected_result and "wait_time" in expected_result:
                wait_time = expected_result["wait_time"]
            time.sleep(wait_time)

    @staticmethod
    def _is_empty_expected_result(expected_result: Dict = None) -> bool:
        """
        判断预期结果是否为空

        Args:
            expected_result: 预期结果字典

        Returns:
            True: 预期结果为空
            False: 预期结果不为空
        """
        if not expected_result:
            return True

        # 检查text字段是否为空
        text_result = expected_result.get("text", "")
        if not text_result or not text_result.strip():
            return True

        return False

    @staticmethod
    def _record_step_execution_history(state: DeploymentState, step_description: str,
                                       ai_response: str, action_command: str, step_status: str):
        """
        记录步骤执行历史

        Args:
            state: 当前状态
            step_description: 步骤描述
            ai_response: AI响应内容
            action_command: 执行的动作命令
            step_status: 步骤状态
        """
        from datetime import datetime

        current_step_index = state.get("current_step_index", 0)

        history_entry = {
            "action": "step_execution_with_ai",
            "step_index": current_step_index,
            "step_description": step_description,
            "ai_response": ai_response,
            "action_command": action_command,
            "step_status": step_status,
            "timestamp": datetime.now().isoformat()
        }

        state["history"].append(history_entry)

    @staticmethod
    def _build_step_execution_prompt(step_description: str, expected_result: Dict = None) -> str:
        """
        构建给大模型的步骤执行提示，包含步骤、预期结果和历史执行记录
        """

        # 安全地获取期望结果文本
        expected_text = ""
        if expected_result:
            expected_text = expected_result.get("text", "")

        prompt = f"""
你是一个GUI助手，你的任务是根据用户指令执行操作，仔细阅读截图和执行历史，给出下一步动作指令

## 用户指令
{step_description}

## 期望结果
{expected_text}

## 可用操作
click(point='<point>x1 y1</point>')  # 点击指定坐标  
long_press(point='<point>x1 y1</point>')  # 长按指定坐标  
type(content='text_to_input')  # 输入文本内容  
scroll(point='<point>x1 y1</point>', direction='down or up or right or left')  # 滑动操作
drag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>')  # 拖动操作  
wait(seconds=wait_seconds)  # 等待指定秒数  
delete(content=delete_count)  # 删除指定数量字符
back()  # 按下返回键  
failed(content='reason')  # 如果尝试 10 次仍无法执行，调用 failed()  
finished(content='success_message')  # 当前步骤成功完成，满足期望结果时调用

## 执行规则
1. 仔细分析当前截图，理解当前界面状态，将当前状态与用户指令的需求进行对比
2. 如果指令要求执行某个动作（如输入、点击等），必须执行，不受历史限制

## 注意事项
- 在 `Thought` 部分必须使用中文  
- 先写一个小计划，最后总结下一步的具体动作
- 必须严格按照输出格式要求输出

## 输出格式
```
Thought: ...
Action: ...
```
"""
        return prompt

    @staticmethod
    def _build_step_execution_messages(ai_prompt: str, current_image_base64: str, state: DeploymentState) -> list:
        """
        构建包含历史对话的消息数组

        Args:
            ai_prompt: 当前的AI提示
            current_image_base64: 当前截图的base64
            state: 当前状态

        Returns:
            包含历史对话的消息列表
        """
        messages = [{
            "role": "user",
            "content": ai_prompt
        }]

        execution_records, has_execution_history = ExecutionAgent._get_history_parameters(state)

        if has_execution_history and execution_records:
            for i, record in enumerate(execution_records):
                messages.append({
                    "role": "assistant",
                    "content": record.get("ai_response", "")
                })
        messages.append({
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{current_image_base64}",
                        "detail": "high"
                    },
                    "detail": "high",
                }
            ]
        })

        return messages

    @staticmethod
    def _parse_ai_response(ai_response: str) -> Dict:
        """
        解析AI响应，提取思考、动作命令和状态

        Returns:
            {
                "thought": "思考内容",
                "action": "动作命令",
                "status": "finished/failed/continue"
            }
        """
        try:
            import re

            result = {
                "thought": "",
                "action": "",
                "status": "continue"
            }

            # 使用正则表达式提取Thought和Action
            thought_match = re.search(r'Thought:\s*(.*?)(?=Action:|$)', ai_response, re.DOTALL | re.IGNORECASE)
            if thought_match:
                result["thought"] = thought_match.group(1).strip()

            action_match = re.search(r'Action:\s*(.*?)(?=```|$)', ai_response, re.DOTALL | re.IGNORECASE)
            if action_match:
                action_text = action_match.group(1).strip()

                # 检查是否是finished或failed动作
                if action_text.lower().startswith("finished"):
                    result["status"] = "finished"
                    result["action"] = action_text
                elif action_text.lower().startswith("failed"):
                    result["status"] = "failed"
                    result["action"] = action_text
                else:
                    result["status"] = "continue"
                    result["action"] = action_text

            # 如果没有找到标准格式，尝试其他解析方式
            if not result["action"]:
                lines = ai_response.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line.startswith("动作:") or line.startswith("Action:"):
                        action_part = line.split(":", 1)[1].strip()
                        result["action"] = action_part
                        if action_part.lower().startswith("finished"):
                            result["status"] = "finished"
                        elif action_part.lower().startswith("failed"):
                            result["status"] = "failed"
                        break

            return result

        except Exception as e:
            logger.warning(f"Failed to parse AI response: {str(e)}")
            return {
                "thought": "",
                "action": "",
                "status": "continue"
            }

    def _execute_parsed_action(self, action_command: str, state: DeploymentState, task_id: str,
                               current_action=None) -> str:
        """
        执行解析后的动作命令，在动作执行前重新截图以确保图片是最新的

        Args:
            action_command: 动作命令
            state: 当前状态
            task_id: 任务ID
            current_action: 当前动作记录对象（用于更新数据库）

        Returns:
            "success": 执行成功
            "failed": 执行失败
            "continue": 继续执行
        """
        try:
            logger.info(f"[{task_id}] 🎯 Executing parsed action: {action_command}")

            # 检查是否是finished或failed状态
            if action_command.lower().startswith("finished"):
                logger.info(f"[{task_id}] ✅ Step marked as finished by AI")
                return "success"
            elif action_command.lower().startswith("failed"):
                logger.error(f"[{task_id}] ❌ Step marked as failed by AI")
                return "failed"

            # 检查动作是否需要坐标，如果需要则在执行后进行图片标记
            needs_coordinates = ExecutionAgent.requires_coordinates(action_command)

            # 直接执行动作命令（坐标已经在上层获取）
            logger.info(f"[{task_id}] ⚡ Executing action command directly")
            from src.domain.ui_task.mobile.android.action_tool import execute_simple_action
            action_result = execute_simple_action(action_command, state["device"])

            # 在动作执行完成后，再重新截一次图用于打标记和更新数据库
            logger.info(f"[{task_id}] 📸 Taking final screenshot after step action execution for annotation and database update...")
            final_step_screenshot_path = take_screenshot(
                device=state["device"],
                task_id=task_id,
                action_name="after_step_action_execution"
            )

            # 无论动作是否成功，都先更新数据库中的截图路径，以便前端刷新展示
            if current_action:
                self._update_action_screenshot(current_action, final_step_screenshot_path)
                logger.info(f"[{task_id}] 📝 Updated database with final screenshot: {final_step_screenshot_path}")

            # 如果动作执行成功且需要坐标，则进行图片标注
            if action_result.get("status") == "success" and needs_coordinates:
                self._annotate_screenshot_after_step_execution(
                    state, action_command, final_step_screenshot_path
                )
            elif action_result.get("status") == "success":
                logger.info(f"[{task_id}] ℹ️ No annotation needed for step action: {action_command}")
            else:
                logger.info(f"[{task_id}] ℹ️ Action failed, skipping annotation but screenshot path updated")

            # 检查执行结果
            if action_result.get("status") == "success":
                logger.info(f"[{task_id}] ✅ Action executed successfully")
                return "success"
            else:
                logger.warning(
                    f"[{task_id}] ⚠️ Action execution failed: {action_result.get('message', 'Unknown error')}")
                return "continue"

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error executing parsed action: {str(e)}")
            return "failed"

    def _check_task_stopped(self, task_id: str) -> bool:
        """
        检查任务是否被停止

        Args:
            task_id: 任务ID

        Returns:
            任务是否被停止
        """
        from src.domain.ui_task.mobile.repo.do.task_stop_manager import task_stop_manager
        return task_stop_manager.is_task_stopped(task_id)

    @staticmethod
    def _get_step_attempt_count(state: DeploymentState, step_index: int) -> int:
        """
        获取指定步骤的尝试次数

        Args:
            state: 当前状态
            step_index: 步骤索引

        Returns:
            该步骤的尝试次数
        """
        history = state.get("history", [])

        # 统计当前步骤的执行记录数量
        step_attempts = [r for r in history if
                         r.get("action") == "step_execution_with_ai" and
                         r.get("step_index") == step_index]

        return len(step_attempts)

    def _update_task_status_to_failed(self, task_id: str, error_message: str):
        """
        更新任务状态为失败

        Args:
            task_id: 任务ID
            error_message: 失败原因
        """
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            # 更新任务状态为失败
            task_persistence_service.update_task_status(
                task_id=task_id,
                status="failed",
                error_message=error_message
            )

            # 记录失败日志
            self._log_execution_error(task_id, "任务执行", error_message)

            logger.info(f"[{task_id}] 📝 Task status updated to failed: {error_message}")

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Failed to update task status: {str(e)}")

    def _annotate_screenshot_after_step_execution(self, state: DeploymentState, action_command: str,
                                                  current_screenshot: str) -> None:
        """
        在分步执行动作完成后对截图进行标注

        Args:
            state: 当前状态
            action_command: 动作命令
            current_screenshot: 当前截图路径
        """
        task_id = state["task_id"]

        try:
            # 执行标注（直接在原文件上标注，不返回路径）
            image_annotator.annotate_screenshot_from_action(
                current_screenshot, action_command, device=state["device"]
            )
            logger.info(f"[{task_id}] 🎯 Screenshot annotated after step execution: {action_command}")

        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to annotate screenshot after step execution: {str(e)}")

    @staticmethod
    def _create_step_action_record(task_id: str, step_description: str):
        """
        创建步骤动作记录

        Args:
            task_id: 任务ID
            step_description: 步骤描述

        Returns:
            创建的动作记录对象
        """
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            current_action = task_persistence_service.create_task_action(
                task_id=task_id,
                step_name=step_description,
                action_command="",  # 稍后更新
                decision_content=""  # 稍后更新
            )

            return current_action

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Failed to create step action record: {str(e)}")
            return None

    @staticmethod
    def _update_action_screenshot(current_action, screenshot_path: str):
        """
        更新动作记录的截图路径

        Args:
            current_action: 动作记录对象
            screenshot_path: 截图路径
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            task_persistence_service.action_repo.update_action_extra_info(
                action_id=current_action.id,
                before_screenshot=screenshot_path
            )

        except Exception as e:
            logger.error(f"Failed to update action screenshot: {str(e)}")

    @staticmethod
    def _update_action_decision_content(current_action, ai_response: str):
        """
        更新动作记录的决策内容

        Args:
            current_action: 动作记录对象
            ai_response: AI响应内容
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.repo.ui_task_repository import DBSessionContext
            from src.domain.ui_task.mobile.repo.dao import UITaskAction
            from datetime import datetime

            with DBSessionContext() as session:
                action = session.query(UITaskAction).filter(UITaskAction.id == current_action.id).first()
                if action:
                    action.decision_content = ai_response
                    action.updated_at = datetime.now()
                    session.commit()

        except Exception as e:
            logger.error(f"Failed to update action decision content: {str(e)}")

    @staticmethod
    def _update_action_command(current_action, action_command: str):
        """
        更新动作记录的动作命令

        Args:
            current_action: 动作记录对象
            action_command: 动作命令
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            task_persistence_service.action_repo.update_action_extra_info(
                action_id=current_action.id,
                final_action_command=action_command
            )

        except Exception as e:
            logger.error(f"Failed to update action command: {str(e)}")

    @staticmethod
    def _complete_step_action_record(current_action, success: bool, message: str = None):
        """
        完成步骤动作记录

        Args:
            current_action: 动作记录对象
            success: 是否成功
            message: 完成消息
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            task_persistence_service.complete_task_action(
                action_id=current_action.id,
                success=success,
                error_message=message if not success else None
            )

            status = "成功" if success else "失败"
            logger.info(f"Step action record completed: {status} - {message or ''}")

        except Exception as e:
            logger.error(f"Failed to complete step action record: {str(e)}")

    @staticmethod
    def _log_step_start(task_id: str, step_description: str):
        """
        记录步骤开始日志

        Args:
            task_id: 任务ID
            step_description: 步骤描述
        """
        try:
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            log_entry = ExecutionLogService.create_system_log(f"开始执行步骤: {step_description}")
            task_persistence_service.append_execution_log_entries(task_id, [log_entry])

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to log step start: {str(e)}")

    @staticmethod
    def _log_ai_decision(task_id: str, step_description: str, ai_response: str):
        """
        记录AI决策日志

        Args:
            task_id: 任务ID
            step_description: 步骤描述
            ai_response: AI响应内容
        """
        try:
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            # 截取AI响应的前500个字符以避免日志过长
            truncated_response = ai_response[:500] + "..." if len(ai_response) > 500 else ai_response
            log_entry = ExecutionLogService.create_decision_log(
                step_name=step_description,
                action_decision=truncated_response,
                action="AI步骤决策"
            )
            task_persistence_service.append_execution_log_entries(task_id, [log_entry])

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to log AI decision: {str(e)}")

    def _log_step_completion(self, task_id: str, step_description: str, success: bool, message: str):
        """
        记录步骤完成日志

        Args:
            task_id: 任务ID
            step_description: 步骤描述
            success: 是否成功
            message: 完成消息
        """
        try:
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            status = "成功" if success else "失败"
            log_entry = ExecutionLogService.create_system_log(f"步骤完成: {step_description} - {status}: {message}")
            task_persistence_service.append_execution_log_entries(task_id, [log_entry])

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to log step completion: {str(e)}")

    @staticmethod
    def _log_action_execution(task_id: str, action_command: str, success: bool, message: str):
        """
        记录动作执行日志

        Args:
            task_id: 任务ID
            action_command: 动作命令
            success: 是否成功
            message: 执行消息
        """
        try:
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            status = "成功" if success else "失败"
            log_entry = ExecutionLogService.create_execution_log(f"动作执行: {action_command} - {status}: {message}")
            task_persistence_service.append_execution_log_entries(task_id, [log_entry])

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to log action execution: {str(e)}")

    @staticmethod
    def _log_execution_error(task_id: str, step_description: str, error_message: str):
        """
        记录执行错误日志

        Args:
            task_id: 任务ID
            step_description: 步骤描述
            error_message: 错误消息
        """
        try:
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            log_entry = ExecutionLogService.create_error_log(f"步骤执行异常: {step_description} - {error_message}")
            task_persistence_service.append_execution_log_entries(task_id, [log_entry])

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to log execution error: {str(e)}")
