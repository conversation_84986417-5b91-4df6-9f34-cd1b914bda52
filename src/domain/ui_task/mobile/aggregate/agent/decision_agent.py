#!/usr/bin/env python3
"""
决策Agent

负责分析截图、生成思考过程和决定下一步动作（不包含坐标）
"""

import base64
import json
import time
from typing import Tuple, Any

from langchain.output_parsers import OutputFixingParser
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from loguru import logger
from pydantic import BaseModel, Field

from src.domain.ui_task.mobile.aggregate.prompt.decision_definition_prompt import *
from src.domain.ui_task.mobile.aggregate.prompt.decision_invoke_prompt import *
from src.domain.ui_task.mobile.android.screenshot_manager import \
    screenshot_manager, convert_screenshot_to_base64
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.domain.ui_task.mobile.utils.exception_handler import TaskExceptionHandler, AgentExceptionHandler
from src.infra.model import get_chat_model


class DecisionResponse(BaseModel):
    """决策Agent响应的数据模型"""
    self_check: str = Field(default="", description="自检流程的自检结果")
    interface_analysis: str = Field(default="", description="当前界面分析")
    current_step_name: str = Field(default="", description="当前正在执行的步骤名称")
    action_decision: str = Field(default="", description="动作决策结果")
    instruction: str = Field(default="", description="操作指令")
    action: str = Field(default="", description="具体动作命令")


class DecisionAgent:
    """决策Agent - 负责分析和决策"""

    def __init__(self):
        self.model = get_chat_model()

        # 创建输出解析器
        self.pydantic_parser = PydanticOutputParser(pydantic_object=DecisionResponse)

        # 尝试使用专门的修复模型，如果不存在则使用默认模型
        self.output_parser = OutputFixingParser.from_llm(
            parser=self.pydantic_parser,
            llm=get_chat_model(model_name="fix")
        )

    def analyze_and_decide(self, state: DeploymentState,
                           before_screenshot_path: str) -> tuple:
        """
            分析截图和测试用例上下文，生成思考过程和决定下一步动作

            Args:
                state: 当前状态
                before_screenshot_path: 截图的base64数据

            Returns:
                Tuple[parsed_fields, action_line]: 解析的JSON字段、动作命令
            """
        # 构建消息
        task_id = state["task_id"]
        try:
            image_data_base64 = convert_screenshot_to_base64(before_screenshot_path, task_id)
            messages = self._build_complete_test_case_messages(state, image_data_base64)
            start_time = time.time()

            prompt = ChatPromptTemplate.from_messages(messages=messages)
            chain = prompt | self.model
            output = chain.invoke({"timeout": 30})
            model_response = output.content
            logger.info(f"[task_id: {task_id}] 决策模型完整响应: \n {model_response}")

            # 解析JSON响应
            parsed_fields, action_line = self._parse_json_response(model_response, state)

            # 立即记录决策日志
            self._log_decision_immediately(parsed_fields, action_line, task_id)

            # 决策结束时间
            end_time = time.time()
            logger.info(f"[task_id: {task_id}] 决策耗时: {end_time - start_time:.2f}s")
            return parsed_fields, action_line
        except Exception as e:
            return AgentExceptionHandler.handle_decision_agent_exception(task_id, e, state)

    def _parse_json_response(self, model_response: str, state: DeploymentState) -> \
            Tuple[Dict[str, Any], str]:
        """
            解析模型的JSON响应，提取各个字段
            使用OutputFixingParser来纠正格式错误的JSON

            Args:
                model_response: 模型的完整响应
                state: 当前状态

            Returns:
                Tuple[parsed_fields, action_line]: 解析的字段字典、动作命令

            Raises:
                Exception: 当JSON解析和修复都失败时抛出异常
            """
        task_id = state['task_id']

        try:
            # 首先尝试直接解析JSON
            json_data = json.loads(model_response.strip())
            logger.info(f"[{task_id}] ✅ Direct JSON parsing successful")

        except json.JSONDecodeError as e:
            logger.warning(f"[{task_id}] ⚠️ Direct JSON parsing failed: {str(e)}")
            logger.info(f"[{task_id}] 🔧 Attempting to fix JSON format using OutputFixingParser...")

            try:
                # 使用OutputFixingParser来修复JSON格式
                parsed_response: DecisionResponse = self.output_parser.parse(model_response)
                json_data = parsed_response.model_dump()
                logger.info(f"[{task_id}] ✅ JSON format fixed successfully using OutputFixingParser")

            except Exception as fix_error:
                logger.error(f"[{task_id}] ❌ OutputFixingParser failed: {str(fix_error)}")
                logger.error(f"[{task_id}] Raw response: {model_response}")

                # 使用统一的异常处理方法
                TaskExceptionHandler.update_task_status_to_failed(
                    task_id,
                    f"JSON parsing and fixing failed in decision agent: {str(fix_error)}"
                )

                # 重新抛出异常，让外层异常处理机制处理
                raise fix_error

        # 获取模型输出的步骤索引和名称
        model_step_index = json_data.get("current_step_index", "")
        model_step_name = json_data.get("current_step_name", "")

        # 根据步骤索引从state中获取完整的步骤名称（去除序号）
        final_step_name = model_step_name
        if model_step_index:
            try:
                step_index = int(model_step_index) - 1  # 转换为0基索引
                task_steps = state.get("task_steps", [])
                if 0 <= step_index < len(task_steps):
                    # 从state中获取完整步骤名称并去除序号前缀
                    full_step_name = task_steps[step_index]
                    final_step_name = self._remove_step_number_prefix(full_step_name)
                    logger.info(
                        f"[{task_id}] 🔄 Replaced model step name '{model_step_name}' with complete step name '{final_step_name}'")
                else:
                    logger.warning(f"[{task_id}] ⚠️ Step index {step_index} out of range, using model output")
            except (ValueError, TypeError):
                logger.warning(f"[{task_id}] ⚠️ Invalid step index '{model_step_index}', using model output")

        # 构建返回的字段字典
        parsed_fields = {
            "self_check": json_data.get("self_check", ""),
            "interface_analysis": json_data.get("interface_analysis", ""),
            "current_step_index": model_step_index,
            "current_step_name": final_step_name,
            "action_decision": json_data.get("action_decision", ""),
            "instruction": json_data.get("instruction", ""),
            "action": json_data.get("action", "")
        }

        action_line = json_data.get("action", "")

        return parsed_fields, action_line

    @staticmethod
    def _remove_step_number_prefix(step_text: str) -> str:
        """
        移除步骤文本前面的序号前缀

        Args:
            step_text: 原始步骤文本

        Returns:
            去除序号后的步骤内容
        """
        if not step_text:
            return step_text

        # 使用正则表达式匹配并移除各种序号格式
        # 匹配格式：数字. 数字) 数字、 数字- 数字空格 等
        import re
        cleaned_text = re.sub(r'^\s*\d+[.)、\-\s]+', '', step_text.strip())
        return cleaned_text.strip()

    def _build_complete_test_case_messages(self, state: DeploymentState,
                                           image_data_base64: str) -> list:

        execution_records, image_records, has_execution_history = self.get_history_parameters(state)

        decision_prompt = get_decision_definition_prompt(state, execution_records, has_execution_history)

        invoke_prompt = get_execution_invoke_prompt()

        messages = [
            {
                "role": "system",
                "content": decision_prompt
            },
            {
                "role": "system",
                "content": invoke_prompt
            }
        ]

        if has_execution_history:
            # 构建历史截图内容列表
            content_list = []

            # 添加历史截图（最近5轮）
            for i, record in enumerate(image_records):
                before_screenshot_path = record.get("before_screenshot")
                if before_screenshot_path and before_screenshot_path != "":
                    print(before_screenshot_path)
                    try:
                        full_path = screenshot_manager.get_screenshot_full_path(before_screenshot_path)
                        with open(full_path, "rb") as f:
                            before_image_content = f.read()
                        before_image_data_base64 = base64.b64encode(before_image_content).decode("utf-8")

                        execution_count = record.get("execution_count", i + 1)
                        content_list.extend([
                            {
                                "type": "text",
                                "text": f"第{execution_count}轮界面截图"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{before_image_data_base64}",
                                    "detail": "high"
                                }
                            }
                        ])
                    except Exception as e:
                        logger.warning(f"Failed to load historical screenshot {before_screenshot_path}: {str(e)}")
                        continue

            # 如果有历史截图，添加包含所有图片的消息
            if content_list:
                messages.append(
                    {
                        "role": "user",
                        "content": "########## 执行记忆界面截图 ##########"
                    }
                )
                content_list.extend([
                    {
                        "type": "text",
                        "text": "########## 当前轮界面截图 ##########"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_data_base64}",
                            "detail": "high"
                        }
                    }
                ])
                messages.append({
                    "role": "user",
                    "content": content_list
                })
            else:
                # 如果没有历史截图，只添加当前截图
                messages.append({
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "########## 当前轮界面截图 ##########"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_data_base64}",
                                "detail": "high"
                            }
                        }
                    ]
                })
        else:
            messages.append({
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "########## 当前轮界面截图 ##########"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_data_base64}"
                        }
                    }
                ]
            })

        messages.append({
            "role": "user",
            "content": get_user_invoke_prompt()
        })
        return messages

    @staticmethod
    def get_history_parameters(state) -> tuple[list[Any], list[Any], bool]:
        history = state.get("history", [])
        execution_records = [r for r in history if
                             r.get("action") == "enhanced_get_location" and
                             (r.get("decision_content") or r.get(
                                 "decision_fields") or r.get("model_response"))]
        image_records = execution_records[-2:] if len(execution_records) > 2 else execution_records
        has_execution_history = len(execution_records) > 0
        print(len(execution_records), len(image_records))
        return execution_records, image_records, has_execution_history

    @staticmethod
    def _log_decision_immediately(parsed_fields: Dict[str, Any], action_line: str, task_id: str):
        """立即记录决策日志"""
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService

            step_name = parsed_fields.get("current_step_name", "")
            action_decision = parsed_fields.get("action_decision", "")

            decision_log = ExecutionLogService.create_decision_log(step_name, action_decision, action_line)
            task_persistence_service.append_execution_log_entries(task_id, [decision_log])
            logger.info(f"[{task_id}] 📝 Decision logged immediately")

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to log decision: {str(e)}")
